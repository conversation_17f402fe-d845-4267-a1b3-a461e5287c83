/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const effectScope: typeof import('vue')['effectScope']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onAddToFavorites: typeof import('@dcloudio/uni-app')['onAddToFavorites']
  const onBackPress: typeof import('@dcloudio/uni-app')['onBackPress']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onError: typeof import('@dcloudio/uni-app')['onError']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onHide: typeof import('@dcloudio/uni-app')['onHide']
  const onLaunch: typeof import('@dcloudio/uni-app')['onLaunch']
  const onLoad: typeof import('@dcloudio/uni-app')['onLoad']
  const onMounted: typeof import('vue')['onMounted']
  const onNavigationBarButtonTap: typeof import('@dcloudio/uni-app')['onNavigationBarButtonTap']
  const onNavigationBarSearchInputChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputChanged']
  const onNavigationBarSearchInputClicked: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputClicked']
  const onNavigationBarSearchInputConfirmed: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputConfirmed']
  const onNavigationBarSearchInputFocusChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputFocusChanged']
  const onPageNotFound: typeof import('@dcloudio/uni-app')['onPageNotFound']
  const onPageScroll: typeof import('@dcloudio/uni-app')['onPageScroll']
  const onPullDownRefresh: typeof import('@dcloudio/uni-app')['onPullDownRefresh']
  const onReachBottom: typeof import('@dcloudio/uni-app')['onReachBottom']
  const onReady: typeof import('@dcloudio/uni-app')['onReady']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onResize: typeof import('@dcloudio/uni-app')['onResize']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onShareAppMessage: typeof import('@dcloudio/uni-app')['onShareAppMessage']
  const onShareTimeline: typeof import('@dcloudio/uni-app')['onShareTimeline']
  const onShow: typeof import('@dcloudio/uni-app')['onShow']
  const onTabItemTap: typeof import('@dcloudio/uni-app')['onTabItemTap']
  const onThemeChange: typeof import('@dcloudio/uni-app')['onThemeChange']
  const onUnhandledRejection: typeof import('@dcloudio/uni-app')['onUnhandledRejection']
  const onUnload: typeof import('@dcloudio/uni-app')['onUnload']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setNesInfo: typeof import('../hooks/common/useBaseNewInfo')['setNesInfo']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useALiAuth: typeof import('../hooks/common/useALiAuth')['useALiAuth']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBaseUrlList: typeof import('../hooks/common/useBaseUrlList')['useBaseUrlList']
  const useBoolean: typeof import('../hooks/common/useBoolean')['default']
  const useCanvasImg: typeof import('../hooks/common/useCanvasImg')['default']
  const useChangeIdent: typeof import('../hooks/common/useIdentitChange')['useChangeIdent']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDeepseeksse: typeof import('../hooks/common/useDeepseeksse')['useDeepseeksse']
  const useDefault: typeof import('../hooks/common/useDefault')['useDefault']
  const useDictionary: typeof import('../hooks/common/useDictionary')['useDictionary']
  const useEaseMobIM: typeof import('../hooks/common/useEaseMobIM')['useEaseMobIM']
  const useExamine: typeof import('../hooks/common/useExamine')['useExamine']
  const useForm: typeof import('../hooks/common/useForm')['useForm']
  const useFormRules: typeof import('../hooks/common/useForm')['useFormRules']
  const useIMConversation: typeof import('../hooks/common/useIMConversation')['useIMConversation']
  const useId: typeof import('vue')['useId']
  const useKeepAlive: typeof import('../hooks/common/useKeepAlive')['useKeepAlive']
  const useKeywordDetection: typeof import('../hooks/common/useKeywordDetection')['useKeywordDetection']
  const useLoading: typeof import('../hooks/common/useLoading')['default']
  const useLocationPermission: typeof import('../hooks/common/useLocationPermission')['useLocationPermission']
  const useLogout: typeof import('../hooks/common/uesLogout')['useLogout']
  const useModel: typeof import('vue')['useModel']
  const useNewInfoAll: typeof import('../hooks/common/useNewInfoAll')['useNewInfoAll']
  const usePagePeriod: typeof import('../hooks/common/usePagePeriod')['usePagePeriod']
  const usePaging: typeof import('../hooks/common/usePaging')['usePaging']
  const usePhoneVersion: typeof import('../hooks/common/usePhoneInfo')['usePhoneVersion']
  const usePosition: typeof import('../hooks/common/usePosition')['usePosition']
  const useShare: typeof import('../hooks/common/useShare')['useShare']
  const useSlots: typeof import('vue')['useSlots']
  const useSmCrypto: typeof import('../hooks/common/useSmCrypto')['useSmCrypto']
  const useSystemInfo: typeof import('../hooks/common/useSystemInfo')['useSystemInfo']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useUserInfo: typeof import('../hooks/common/useUserInfo')['useUserInfo']
  const useWebsocket: typeof import('../hooks/common/useWebsocket')['default']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface GlobalComponents {}
  interface ComponentCustomProperties {
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onAddToFavorites: UnwrapRef<typeof import('@dcloudio/uni-app')['onAddToFavorites']>
    readonly onBackPress: UnwrapRef<typeof import('@dcloudio/uni-app')['onBackPress']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onError: UnwrapRef<typeof import('@dcloudio/uni-app')['onError']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onHide: UnwrapRef<typeof import('@dcloudio/uni-app')['onHide']>
    readonly onLaunch: UnwrapRef<typeof import('@dcloudio/uni-app')['onLaunch']>
    readonly onLoad: UnwrapRef<typeof import('@dcloudio/uni-app')['onLoad']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onNavigationBarButtonTap: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarButtonTap']>
    readonly onNavigationBarSearchInputChanged: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputChanged']>
    readonly onNavigationBarSearchInputClicked: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputClicked']>
    readonly onNavigationBarSearchInputConfirmed: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputConfirmed']>
    readonly onNavigationBarSearchInputFocusChanged: UnwrapRef<typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputFocusChanged']>
    readonly onPageNotFound: UnwrapRef<typeof import('@dcloudio/uni-app')['onPageNotFound']>
    readonly onPageScroll: UnwrapRef<typeof import('@dcloudio/uni-app')['onPageScroll']>
    readonly onPullDownRefresh: UnwrapRef<typeof import('@dcloudio/uni-app')['onPullDownRefresh']>
    readonly onReachBottom: UnwrapRef<typeof import('@dcloudio/uni-app')['onReachBottom']>
    readonly onReady: UnwrapRef<typeof import('@dcloudio/uni-app')['onReady']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onResize: UnwrapRef<typeof import('@dcloudio/uni-app')['onResize']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onShareAppMessage: UnwrapRef<typeof import('@dcloudio/uni-app')['onShareAppMessage']>
    readonly onShareTimeline: UnwrapRef<typeof import('@dcloudio/uni-app')['onShareTimeline']>
    readonly onShow: UnwrapRef<typeof import('@dcloudio/uni-app')['onShow']>
    readonly onTabItemTap: UnwrapRef<typeof import('@dcloudio/uni-app')['onTabItemTap']>
    readonly onThemeChange: UnwrapRef<typeof import('@dcloudio/uni-app')['onThemeChange']>
    readonly onUnhandledRejection: UnwrapRef<typeof import('@dcloudio/uni-app')['onUnhandledRejection']>
    readonly onUnload: UnwrapRef<typeof import('@dcloudio/uni-app')['onUnload']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('vue')['onWatcherCleanup']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setNesInfo: UnwrapRef<typeof import('../hooks/common/useBaseNewInfo')['setNesInfo']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly useALiAuth: UnwrapRef<typeof import('../hooks/common/useALiAuth')['useALiAuth']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useBaseUrlList: UnwrapRef<typeof import('../hooks/common/useBaseUrlList')['useBaseUrlList']>
    readonly useBoolean: UnwrapRef<typeof import('../hooks/common/useBoolean')['default']>
    readonly useCanvasImg: UnwrapRef<typeof import('../hooks/common/useCanvasImg')['default']>
    readonly useChangeIdent: UnwrapRef<typeof import('../hooks/common/useIdentitChange')['useChangeIdent']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useDeepseeksse: UnwrapRef<typeof import('../hooks/common/useDeepseeksse')['useDeepseeksse']>
    readonly useDefault: UnwrapRef<typeof import('../hooks/common/useDefault')['useDefault']>
    readonly useDictionary: UnwrapRef<typeof import('../hooks/common/useDictionary')['useDictionary']>
    readonly useEaseMobIM: UnwrapRef<typeof import('../hooks/common/useEaseMobIM')['useEaseMobIM']>
    readonly useExamine: UnwrapRef<typeof import('../hooks/common/useExamine')['useExamine']>
    readonly useForm: UnwrapRef<typeof import('../hooks/common/useForm')['useForm']>
    readonly useFormRules: UnwrapRef<typeof import('../hooks/common/useForm')['useFormRules']>
    readonly useIMConversation: UnwrapRef<typeof import('../hooks/common/useIMConversation')['useIMConversation']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useKeepAlive: UnwrapRef<typeof import('../hooks/common/useKeepAlive')['useKeepAlive']>
    readonly useKeywordDetection: UnwrapRef<typeof import('../hooks/common/useKeywordDetection')['useKeywordDetection']>
    readonly useLoading: UnwrapRef<typeof import('../hooks/common/useLoading')['default']>
    readonly useLocationPermission: UnwrapRef<typeof import('../hooks/common/useLocationPermission')['useLocationPermission']>
    readonly useLogout: UnwrapRef<typeof import('../hooks/common/uesLogout')['useLogout']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useNewInfoAll: UnwrapRef<typeof import('../hooks/common/useNewInfoAll')['useNewInfoAll']>
    readonly usePagePeriod: UnwrapRef<typeof import('../hooks/common/usePagePeriod')['usePagePeriod']>
    readonly usePaging: UnwrapRef<typeof import('../hooks/common/usePaging')['usePaging']>
    readonly usePhoneVersion: UnwrapRef<typeof import('../hooks/common/usePhoneInfo')['usePhoneVersion']>
    readonly usePosition: UnwrapRef<typeof import('../hooks/common/usePosition')['usePosition']>
    readonly useShare: UnwrapRef<typeof import('../hooks/common/useShare')['useShare']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useSmCrypto: UnwrapRef<typeof import('../hooks/common/useSmCrypto')['useSmCrypto']>
    readonly useSystemInfo: UnwrapRef<typeof import('../hooks/common/useSystemInfo')['useSystemInfo']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useUserInfo: UnwrapRef<typeof import('../hooks/common/useUserInfo')['useUserInfo']>
    readonly useWebsocket: UnwrapRef<typeof import('../hooks/common/useWebsocket')['default']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}
