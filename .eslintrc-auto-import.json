{"globals": {"Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "MaybeRef": true, "MaybeRefOrGetter": true, "PropType": true, "Ref": true, "VNode": true, "WritableComputedRef": true, "computed": true, "createApp": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "getCurrentInstance": true, "getCurrentScope": true, "h": true, "inject": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "markRaw": true, "nextTick": true, "onActivated": true, "onAddToFavorites": true, "onBackPress": true, "onBeforeMount": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onError": true, "onErrorCaptured": true, "onHide": true, "onLaunch": true, "onLoad": true, "onMounted": true, "onNavigationBarButtonTap": true, "onNavigationBarSearchInputChanged": true, "onNavigationBarSearchInputClicked": true, "onNavigationBarSearchInputConfirmed": true, "onNavigationBarSearchInputFocusChanged": true, "onPageNotFound": true, "onPageScroll": true, "onPullDownRefresh": true, "onReachBottom": true, "onReady": true, "onRenderTracked": true, "onRenderTriggered": true, "onResize": true, "onScopeDispose": true, "onServerPrefetch": true, "onShareAppMessage": true, "onShareTimeline": true, "onShow": true, "onTabItemTap": true, "onThemeChange": true, "onUnhandledRejection": true, "onUnload": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "resolveComponent": true, "setNesInfo": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "unref": true, "useALiAuth": true, "useAttrs": true, "useBaseUrlList": true, "useBoolean": true, "useCanvasImg": true, "useChangeIdent": true, "useCssModule": true, "useCssVars": true, "useDeepseeksse": true, "useDefault": true, "useDictionary": true, "useEaseMobIM": true, "useExamine": true, "useForm": true, "useFormRules": true, "useIMConversation": true, "useId": true, "useKeepAlive": true, "useKeywordDetection": true, "useLoading": true, "useLocationPermission": true, "useLogout": true, "useModel": true, "useNewInfoAll": true, "usePagePeriod": true, "usePaging": true, "usePhoneVersion": true, "usePosition": true, "useShare": true, "useSlots": true, "useSmCrypto": true, "useSystemInfo": true, "useTemplateRef": true, "useUserInfo": true, "useWebsocket": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true}}