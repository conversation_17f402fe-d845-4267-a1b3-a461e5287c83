<template>
  <common-card>
    <view class="p-[24rpx_30rpx_0]" @tap="handleToPreviewDetail">
      <view class="flex flex-col gap-12rpx">
        <view class="flex items-center gap-26rpx">
          <wd-skeleton
            :loading="skeletonLoading"
            :row-col="[[{ width: '82rpx', height: '82rpx', type: 'circle' }]]"
          >
            <view
              :class="
                hxUserInfo?.isOnline || personalList?.ActivityStatus === 1
                  ? 'border-twinkle bg_left_icon_box'
                  : ''
              "
            >
              <wd-img :src="hxUserInfo?.avatar || avatarOne" height="82rpx" round width="82rpx" />
            </view>
          </wd-skeleton>
          <view class="flex flex-col gap-6rpx flex-1">
            <view class="c-#333333 flex items-center">
              <view class="flex-1 flex items-center">
                <text class="text-38rpx font-500">
                  {{ hxUserInfo?.nickname }}
                </text>
                <!-- <view class="flex items-center" v-if="hxUserInfo?.isOnline">
                  <view class="mx-8rpx size-6rpx bg-#34A715 rounded-full" />
                  <text class="c-#34A715 text-24rpx">在线</text>
                </view> -->
                <view
                  v-if="personalList?.ActivityStatus"
                  :class="personalList?.ActivityStatus === 1 ? 'c-#0ea500' : 'c-#666'"
                  class="flex items-center text-24rpx"
                >
                  <view
                    :class="personalList?.ActivityStatus === 1 ? 'bg-#0ea500' : 'bg-#666'"
                    class="mx-8rpx size-6rpx rounded-full"
                  />
                  <text>{{ personOtherInfo?.ActivityStatus }}</text>
                </view>
              </view>
              <text class="c-#FF8080 text-32rpx font500">
                {{
                  (() => {
                    const salaryRange = [
                      formatToKilo(personalList.salaryExpectationStart),
                      formatToKilo(personalList.salaryExpectationEnd),
                    ]
                      .filter(Boolean)
                      .join('-')
                    return salaryRange ? `${salaryRange} /月` : '面议'
                  })()
                }}
              </text>
            </view>
            <text class="c-#333333 text-24rpx line-clamp-1">
              {{
                [personOtherInfo.seekStatus, personOtherInfo.qualification, personalList.major]
                  .filter(Boolean)
                  .join(' | ')
              }}
            </text>
          </view>
        </view>
        <view
          v-if="personalList?.workExperienceList?.length"
          class="flex flex-col gap-12rpx relative"
        >
          <view
            v-for="(item, key) in (isWorkExperienceExpanded
              ? personalList?.workExperienceList
              : personalList?.workExperienceList?.slice(0, 1)) ?? []"
            :key="`workExperienceList-${key}`"
            class="flex items-center gap-16rpx relative"
          >
            <wd-icon v-if="key === 0" :name="postMark" size="26rpx" />
            <view v-else class="dot-separator" />

            <view class="flex items-center flex-1 line-clamp-1">
              <text class="c-#333333 text-24rpx">{{ item?.workCompanyName }}</text>
              <view
                v-if="item?.workCompanyName && (item?.workYears || !(item?.workYears ?? 0))"
                class="dot-separator"
              />
              <text class="c-#333333 text-24rpx">
                {{ !(item?.workYears ?? 0) ? '1年内' : `${item?.workYears}年以上` }}
              </text>
            </view>
            <text
              v-if="!key && personalList?.workExperienceList?.length > 1"
              :class="
                isWorkExperienceExpanded
                  ? 'i-carbon-triangle-solid'
                  : 'i-carbon-triangle-down-solid'
              "
              class="c-#000000 text-16rpx"
              @tap.stop="
                !key && personalList?.workExperienceList?.length > 1
                  ? toggleWorkExperience()
                  : undefined
              "
            />
          </view>
          <!--          &lt;!&ndash; 虚线连接器 &ndash;&gt;-->
          <!--          <view-->
          <!--            v-if="isWorkExperienceExpanded && personalList?.workExperienceList?.length > 1"-->
          <!--            class="absolute left-13rpx top-40rpx bottom-28rpx w-0 border-l-2 border-l-dashed border-l-#d8d8d8"-->
          <!--            style="z-index: 0"-->
          <!--          />-->
        </view>
      </view>
      <view class="flex flex-col gap-12rpx mt-12rpx mb-12rpx">
        <text v-if="personalList?.myLights" class="c-#333333 text-24rpx line-clamp-1">
          {{ personalList.myLights }}
        </text>
        <view class="flex items-center flex-wrap gap-20rpx">
          <view
            v-for="(item, index) in splitToArray(personalList?.certificateNames)"
            :key="index"
            class="bg-#F3F3F3 border-rd-6rpx h-46rpx min-w-150rpx px-20rpx center"
          >
            <text class="c-#333333 text-24rpx">{{ item }}</text>
          </view>
        </view>
      </view>
      <view
        class="flex items-center h-32rpx gap-10rpx py-30rpx mx--30rpx px-30rpx border-t-1 border-t-dashed border-t-#D8D8D8"
      >
        <view
          class="flex items-center justify-center min-w-168rpx h-32rpx relative p-1px"
          style="
            clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%);
            background: linear-gradient(270deg, rgba(255, 86, 86, 1), rgba(81, 150, 253, 1));
          "
        >
          <view
            class="size-full bg-#ffffff flex items-center px-8rpx gap-4rpx"
            style="clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%)"
          >
            <wd-img :src="resumeMatching" height="20rpx" width="88rpx" />
            <text class="text-20rpx c-#DF6176 font-500">{{ progressWidth }}%</text>
          </view>
        </view>
        <view class="flex-1 flex items-center">
          <view
            class="w-182rpx h-24rpx shadow-[0rpx_0rpx_2rpx_0rpx_#FF8A8A] border-rd-full p-1px"
            style="
              background: linear-gradient(
                139deg,
                rgba(217, 182, 253, 1),
                rgba(162, 199, 253, 1),
                rgba(153, 193, 253, 1),
                rgba(250, 112, 145, 1)
              );
            "
          >
            <view class="w-full h-full bg-white border-rd-full px-6rpx flex items-center">
              <view
                :style="{
                  width: `${progressWidth}%`,
                  background:
                    'linear-gradient(315deg, #FF6C8C 0%, #9AC2FD 47%, rgba(90,155,252,0.55) 85%, rgba(141,34,251,0.33) 98%)',
                }"
                class="h-14rpx border-rd-full transition-all duration-1000 ease-in-out"
              />
            </view>
          </view>
        </view>

        <view class="relative flex items-center gap4rpx">
          <view
            class="flex items-center justify-center min-w-168rpx h-32rpx p-1px"
            style="
              clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%);
              background: linear-gradient(270deg, rgba(255, 86, 86, 1), rgba(81, 150, 253, 1));
            "
          >
            <view
              class="size-full bg-#ffffff flex items-center px-8rpx gap-10rpx"
              style="clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%)"
            >
              <wd-img :src="resumeCompetitiveness" height="20rpx" width="88rpx" />
              <text class="text-20rpx font-bold c-#DF6176">
                {{ displayActivityTotal }}
              </text>
            </view>
          </view>
          <view class="line-height-26rpx flex items-center relative overflow-visible">
            <text
              :class="{ animate: animationBool }"
              class="c-#F7585D text-20rpx font-bold plus-one-main"
            >
              +1
            </text>
            <text
              :class="{ animate: animationBool }"
              class="c-#F7585D text-20rpx font-bold plus-one-shadow absolute"
            >
              +1
            </text>
          </view>
          <view class="absolute left-88rpx bottom--12rpx">
            <wd-icon :name="resumeCompetitivenessStar" size="20rpx" />
          </view>
        </view>
      </view>
    </view>
  </common-card>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { splitToArray, formatToKilo } from '@/utils'
import { DICT_IDS } from '@/enum'
import { UserInfoWithPresence } from '@/ChatUIKit/types'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'
import commonCard from './common-card.vue'
import avatarOne from '@/static/common/avatar/1.png'
import postMark from '@/static/common/post-mark.png'
import resumeMatching from '@/static/common/resume-matching.png'
import resumeCompetitiveness from '@/static/common/resume-competitiveness.png'
import resumeCompetitivenessStar from '@/static/common/resume-competitiveness-star.png'

interface propsInt {
  list: hrIndexResumeUserListInt
  position: hrPositionQueryOptionListInt
}

const props = withDefaults(defineProps<propsInt>(), {
  list: () => ({}) as hrIndexResumeUserListInt,
})
const { getDictLabel } = useDictionary()
const { progressWidth, startProgress } = useProgress()
const { bool: animationBool, setTrue: setAnimationTrue, setFalse: setAnimationFalse } = useBoolean()
const {
  bool: skeletonLoading,
  setTrue: setSkeletonLoadingTrue,
  setFalse: setSkeletonLoadingFalse,
} = useBoolean()
const { bool: isWorkExperienceExpanded, toggle: toggleWorkExperience } = useBoolean()

const personOtherInfo = reactive({
  /** 学历 */
  qualification: '',
  /** 求职状态 */
  seekStatus: '',
  /** 活动状态 */
  ActivityStatus: '',
})
const personalList = computed(() => props.list)

console.log('personalList', personalList.value)

const displayActivityTotal = computed(() => {
  const total = personalList.value?.activityTotal
  if (!total) {
    return Math.floor(Math.random() * 101) + 100 // 100-200之间随机数
  }
  return total > 999 ? '999+' : total
})
const hxUserInfo = ref<Partial<UserInfoWithPresence>>({})

async function getHxUserInfo() {
  try {
    setSkeletonLoadingTrue()
    const { hxUserInfoVO } = personalList.value
    const { username } = hxUserInfoVO
    try {
      await uni.$UIKit.appUserStore.getUsersPresenceFromServer({
        userIdList: [username],
      })
    } catch (error) {
      console.log('获取在线状态失败', error)
    }

    await uni.$UIKit.appUserStore.getUsersInfoFromServer({
      userIdList: [username],
    })
    const userInfo = uni.$UIKit.appUserStore.getUserInfoFromStore(username)
    // console.log('==============================================================')
    // console.log(userInfo, 'userInfo===============')
    hxUserInfo.value = userInfo
    setSkeletonLoadingFalse()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

function useProgress() {
  const progressWidth = ref(0)
  const startProgress = () => {
    const randomProgress = Math.floor(Math.random() * 20) + 80
    setTimeout(() => {
      progressWidth.value = randomProgress
    }, 100)
  }
  return { progressWidth, startProgress }
}

function handleToPreviewDetail() {
  const { userId, resumeBaseInfoId } = personalList.value
  const { id: positionInfoId, positionName } = props.position
  const hrDetailItem = JSON.stringify({
    positionInfoId,
    positionName,
    resumeBaseInfoId,
    userId,
  })
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/resumeRelated/preview/index', {
      hrDetailItem,
    }),
  })
}

async function getQualification() {
  personOtherInfo.qualification = (await getDictLabel(
    DICT_IDS.EDUCATION_REQUIREMENT,
    personalList.value.qualification,
  )) as string
}

async function getSeekStatus() {
  personOtherInfo.seekStatus = (await getDictLabel(
    DICT_IDS.SEEK_STATUS,
    personalList.value.seekStatus,
  )) as string
}

async function getActivityStatus() {
  personOtherInfo.ActivityStatus = (await getDictLabel(
    DICT_IDS.ONLINE_STATUS,
    personalList.value.ActivityStatus,
  )) as string
}

let animationTimer: number | null = null
let animationInterval: number | null = null

function startPlusOneAnimation() {
  if (animationTimer) clearTimeout(animationTimer)
  if (animationInterval) clearInterval(animationInterval)
  const triggerAnimation = () => {
    personalList.value.activityTotal++
    setAnimationTrue()
    animationTimer = setTimeout(() => {
      setAnimationFalse()
    }, 1500)
  }
  animationTimer = setTimeout(triggerAnimation, 1000)
  animationInterval = setInterval(triggerAnimation, 3000)
}

function stopPlusOneAnimation() {
  if (animationTimer) {
    clearTimeout(animationTimer)
    animationTimer = null
  }
  if (animationInterval) {
    clearInterval(animationInterval)
    animationInterval = null
  }
  setAnimationFalse()
}

onMounted(() => {
  getQualification()
  getSeekStatus()
  getActivityStatus()
  startProgress()
  startPlusOneAnimation()
  getHxUserInfo()
})

onUnmounted(() => {
  stopPlusOneAnimation()
})
</script>

<style lang="scss" scoped>
.plus-one-main {
  position: relative;
  z-index: 3;
  transition: transform 0.3s ease;
}

.plus-one-shadow {
  z-index: 2;
  opacity: 0;
  transform: translate(0, 0) scale(1);

  &.animate {
    animation: shadowFloat 1.5s ease-out;
  }
}

.border-twinkle {
  position: relative;

  &::before {
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    left: -2rpx;
    z-index: -1;
    content: '';
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
    border-radius: 50rpx;
    animation: twinkle 6s infinite;
  }
}

.bg_left_icon_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 86rpx;
  height: 86rpx;
  border: 3rpx solid #0ea500;
  border-radius: 50rpx;
}

@keyframes shadowFloat {
  0% {
    opacity: 0;
    transform: translate(0, 0) scale(1);
  }
  20% {
    opacity: 0.6;
    transform: translate(8rpx, -12rpx) scale(0.9);
  }
  100% {
    opacity: 0;
    transform: translate(16rpx, -24rpx) scale(0.6);
  }
}

.dot-separator {
  flex-shrink: 0;
  width: 6rpx;
  height: 6rpx;
  margin: 4rpx 11rpx 0 11rpx;
  background-color: #666;
  border-radius: 50%;
}
</style>
