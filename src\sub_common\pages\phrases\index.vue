<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '常用语',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    :paging-style="pageStyle"
    safe-area-inset-bottom
    :loading-more-enabled="false"
    :refresher-enabled="!sortBool"
    auto-scroll-to-top-when-reload
    @query="queryList"
  >
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          @click-left="handleClickLeft"
          @click-right="handleClickRight"
          custom-class="px-25rpx"
        >
          <template #title>
            <view class="center">
              <view class="w-300rpx">
                <wd-tabs
                  v-model="phrasesTabsStatus"
                  line-width="130rpx"
                  line-height="6rpx"
                  color="#333333"
                  inactive-color="#666666"
                  @change="handleChangeTabs"
                >
                  <wd-tab
                    v-for="item in phrasesTabsList"
                    :key="item.name"
                    :title="`${item.label}`"
                    :name="item.name"
                  />
                </wd-tabs>
              </view>
            </view>
          </template>
          <template #right v-if="phrasesIsCommon">
            <text class="c-#333333 text-28rpx">{{ sortBool ? '完成' : '排序' }}</text>
          </template>
        </wd-navbar>
        <view class="px-50rpx mt-44rpx" v-if="!phrasesIsCommon">
          <view
            class="rounded-20rpx bg-#ffffff overflow-hidden shadow-[0rpx_8rpx_29rpx_0rpx_rgba(0,0,0,0.1)]"
          >
            <wd-textarea v-model="greetContent" placeholder="请设置招呼语" :maxlength="30" />
            <view class="flex items-center px-15px pb-26rpx">
              <text class="c-#9E9E9E text-24rpx flex-1">点击下方设置招呼语</text>
              <wd-icon :name="phrasesEdit" size="40rpx" @click="handleSaveGreet" />
            </view>
          </view>
        </view>
      </wd-config-provider>
    </template>
    <view class="px-50rpx">
      <wd-config-provider :themeVars="themeVars">
        <view class="my-44rpx" v-if="phrasesIsCommon">
          <l-drag v-if="sortBool" :list="pageData" :column="1" @change="handleDragPhrase">
            <template #grid="{ content }">
              <common-card :shadow="false">
                <view class="px-36rpx py-18rpx flex flex-col gap-16rpx">
                  <view class="flex items-center gap-38rpx">
                    <text
                      class="c-#333333 text-24rpx whitespace-pre-wrap line-height-44rpx flex-1 line-clamp-1"
                    >
                      {{ content.content }}
                    </text>
                    <view class="center bg-#E1EBFF rounded-10rpx size-60rpx" v-if="sortBool">
                      <wd-icon :name="phrasesSort" size="32rpx" />
                    </view>
                  </view>
                  <view class="flex items-center" v-if="!sortBool">
                    <view class="flex-1">
                      <wd-checkbox
                        :model-value="!!content.isDefault"
                        checked-color="#075EFF"
                        @change="handleSetDefault(content)"
                      >
                        <text class="c-#075EFF text-24rpx">设为默认</text>
                      </wd-checkbox>
                    </view>
                    <view class="flex items-center gap-44rpx">
                      <wd-icon
                        :name="phrasesEdit"
                        size="40rpx"
                        @click="handleAddPhrase('edit', content)"
                      />
                      <wd-icon :name="phrasesDelete" size="40rpx" />
                    </view>
                  </view>
                </view>
              </common-card>
            </template>
          </l-drag>
          <div class="flex flex-col gap-30rpx" v-else>
            <common-card v-for="(item, key) in pageData" :key="`phrases-${key}`">
              <view class="px-36rpx py-18rpx flex flex-col gap-16rpx">
                <view class="flex items-center gap-38rpx">
                  <text
                    class="c-#333333 text-24rpx whitespace-pre-wrap line-height-44rpx flex-1 line-clamp-1"
                  >
                    {{ item.content }}
                  </text>
                  <view class="center bg-#E1EBFF rounded-10rpx size-60rpx" v-if="sortBool">
                    <wd-icon :name="phrasesSort" size="32rpx" />
                  </view>
                </view>
                <view class="flex items-center" v-if="!sortBool">
                  <view class="flex-1">
                    <wd-checkbox
                      :model-value="!!item.isDefault"
                      checked-color="#075EFF"
                      @change="handleSetDefault(item)"
                    >
                      <text class="c-#075EFF text-24rpx">设为默认</text>
                    </wd-checkbox>
                  </view>
                  <view class="flex items-center gap-44rpx">
                    <wd-icon
                      :name="phrasesEdit"
                      size="40rpx"
                      @click="handleAddPhrase('edit', item)"
                    />
                    <wd-icon
                      :name="phrasesDelete"
                      @click="handleAddPhrase('delete', item)"
                      size="40rpx"
                    />
                  </view>
                </view>
              </view>
            </common-card>
          </div>
        </view>
        <view class="flex flex-col mx--50rpx" v-else>
          <view
            v-for="(item, key) in pageData"
            :key="`phrases-greet-${key}`"
            class="py-40rpx px-50rpx border-b-1px border-b-solid border-b-[#D4D4D4]"
            @click="handleSelectGreet(item)"
          >
            <text class="c-#333333 text-24rpx whitespace-pre-wrap">
              {{ item.content }}
            </text>
          </view>
        </view>
      </wd-config-provider>
    </view>
    <template #bottom>
      <view class="px-86rpx py-20rpx" v-if="phrasesIsCommon">
        <wd-button
          size="large"
          custom-class="w-full !h-92rpx !bg-#075EFF"
          @click="handleAddPhrase('add')"
        >
          <text class="text-28rpx c-#FFFFFF">添加常用语</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, useMessage, type ConfigProviderThemeVars } from 'wot-design-uni'
import {
  sysUserCommonPhraseQueryList,
  sysUserCommonPhraseAdd,
  sysUserCommonPhraseUpdate,
  sysUserCommonPhraseReSort,
  sysUserCommonPhraseDeleteById,
} from '@/service/sysUserCommonPhrase'
import { sysUserCallQueryOne, sysUserCallUpdate } from '@/service/sysUserCall'
import { usePhrases } from '@/sub_common/hooks/usePhrases'
import { EMIT_EVENT } from '@/enum'
import type {
  sysUserCommonPhraseQueryListInt,
  sysUserCommonPhraseReSortDataInt,
} from '@/service/sysUserCommonPhrase/types'
import commonCard from '@/components/common/common-card.vue'
import LDrag from '@/uni_modules/lime-drag/components/l-drag/l-drag.vue'
import phrasesEdit from '@/sub_common/static/phrases/phrases-edit.png'
import phrasesDelete from '@/sub_common/static/phrases/phrases-delete.png'
import phrasesSort from '@/sub_common/static/phrases/phrases-sort.png'

const toast = useToast()
const message = useMessage()
const { pageParams } = usePagePeriod()
const {
  bool: sortBool,
  setFalse: setSortFalse,
  setTrue: setSortTrue,
  toggle: toggleSort,
} = useBoolean()
const {
  phrasesCommonFilterInfo,
  phrasesCommonList,
  setPhrasesCommonActive,
  deleteLocalPhraseData,
} = usePhrases()
const { pagingRef, pageStyle, pageInfo, pageSetInfo, pageData } =
  usePaging<sysUserCommonPhraseQueryListInt>({
    style: {
      background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
    },
  })
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: ' transparent',
  navbarArrowSize: '30rpx',
  tabsNavLineBgColor: 'linear-gradient( 270deg, #FFC2C2 0%, #DDDCFF 100%)',
  tabsNavFs: '28rpx',
  checkboxLabelMargin: '6rpx',
}
const greetContent = ref('')
const updatePhrase = ref<sysUserCommonPhraseReSortDataInt['commonPhraseList']>([])
const phrasesTabsStatus = ref(0)
const phrasesTabsList = [
  {
    label: '常用语',
    name: 0,
    api: async () => {
      await fetchSysUserCommonPhraseQueryList()
    },
  },
  {
    label: '招呼语',
    name: 1,
    api: async () => {
      const { data } = await sysUserCallQueryOne({})
      greetContent.value = data || ''
    },
  },
]
const phrasesIsCommon = computed(() => phrasesTabsStatus.value === 0)
const phrasesTabsActive = computed(() =>
  phrasesTabsList.find((item) => item.name === phrasesTabsStatus.value),
)
async function handleClickRight() {
  if (sortBool.value) {
    await sysUserCommonPhraseReSort({
      commonPhraseList: updatePhrase.value,
    })
    toast.show('排序成功')
    reload()
  }
  toggleSort()
}
function handleClickLeft() {
  uni.navigateBack()
}

async function fetchSysUserCommonPhraseQueryList() {
  const { data } = await sysUserCommonPhraseQueryList({ ...pageInfo })
  const { list } = data
  phrasesCommonList.value = CommonUtil.deepClone(phrasesCommonFilterInfo(list))
  pagingRef.value.completeByNoMore(phrasesCommonList.value, true)
}

function queryList(page: number) {
  pageSetInfo(page, 20)
  phrasesTabsActive.value.api()
}
function handleChangeTabs({ name }) {
  phrasesTabsStatus.value = name
  pagingRef.value.reload()
}
async function handleDragPhrase(list: { content: sysUserCommonPhraseQueryListInt }[]) {
  updatePhrase.value = list.map(({ content }) => {
    return {
      id: content.id,
      content: content.content,
      systemDefaultKey: content.systemDefaultKey,
    }
  })
}
function handleAddPhrase(type: 'add' | 'edit' | 'delete', item?: sysUserCommonPhraseQueryListInt) {
  const isEdit = type === 'edit'
  const isDelete = type === 'delete'
  if (isDelete) {
    message
      .confirm({
        title: '提示',
        msg: '是否删除该常用语？',
        confirmButtonText: '删除',
        cancelButtonText: '取消',
      })
      .then(async () => {
        await handleDeletePhrase(item!)
      })
      .catch(() => {
        // 用户取消删除操作
      })
    return
  }
  if (isEdit) {
    setPhrasesCommonActive(item)
  }
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/sub_common/pages/phrases/add', {
      type,
    }),
  })
}

async function handleDeletePhrase(item: sysUserCommonPhraseQueryListInt) {
  try {
    const hasId = item.id !== null && item.id !== undefined
    const hasSystemDefaultKey =
      item.systemDefaultKey !== null &&
      item.systemDefaultKey !== undefined &&
      item.systemDefaultKey !== ''
    if (!hasId && hasSystemDefaultKey) {
      deleteLocalPhraseData(item.systemDefaultKey)
      toast.show('删除成功')
      reload()
    } else if (hasId && !hasSystemDefaultKey) {
      await sysUserCommonPhraseDeleteById({ id: item.id })
      toast.show('删除成功')
      reload()
    } else if (hasId && hasSystemDefaultKey) {
      await sysUserCommonPhraseDeleteById({ id: item.id })
      deleteLocalPhraseData(item.systemDefaultKey)
      toast.show('删除成功')
      reload()
    } else {
      console.warn('删除失败：数据状态异常', item)
      toast.show('删除失败')
    }
  } catch (error) {
    console.error('删除常用语失败:', error)
    toast.show('删除失败')
  }
}

async function handleSetDefault(item: sysUserCommonPhraseQueryListInt) {
  if (item.isDefault) return
  pageData.value.forEach((phrase) => {
    phrase.isDefault = 0
  })
  if (!item.id) {
    await sysUserCommonPhraseAdd({
      content: item.content,
      isDefault: 1,
      systemDefaultKey: item?.systemDefaultKey ?? null,
    })
  } else {
    await sysUserCommonPhraseUpdate({
      ...item,
      isDefault: 1,
    })
  }
  item.isDefault = 1
  toast.show('设置成功')
}
const handleSelectGreet = CommonUtil.debounce(async (item: sysUserCommonPhraseQueryListInt) => {
  greetContent.value = item.content
  await sysUserCallUpdate({
    content: greetContent.value,
  })
  toast.show('设置成功')
}, 300)
const handleSaveGreet = CommonUtil.debounce(async () => {
  if (!greetContent.value) {
    toast.show('请输入招呼语')
    return
  }
  await sysUserCallUpdate({
    content: greetContent.value,
  })
  toast.show('设置成功')
}, 300)
async function reload() {
  await uni.$onLaunched
  pagingRef.value.reload()
}
uni.$on(EMIT_EVENT.REFRESH_COMMON_PHRASES, reload)
onMounted(() => {
  reload()
})
onUnmounted(() => {
  uni.$off(EMIT_EVENT.REFRESH_COMMON_PHRASES)
})
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__line {
    bottom: 6px;
  }
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    font-weight: 500;
    &.is-active {
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

:deep(.wd-textarea) {
  .wd-textarea__inner {
    height: 100rpx;
  }
}
</style>
