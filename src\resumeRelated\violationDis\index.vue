<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="violation-container">
    <CustomNavBar title="违规公示"></CustomNavBar>
    <view class="auto-scroll-container">
      <scroll-view :scroll-top="scrollTop" class="scroll-view" scroll-y @scroll="onScroll">
        <view :style="{ transform: `translateY(${translateY}px)` }" class="qualiCertificate-list">
          <!-- 渲染两份数据实现无缝滚动 -->
          <view
            v-for="(item, index) in [...pageData, ...pageData]"
            :key="`${index}-${Math.floor(index / pageData.length)}`"
            class="qualiCertificate-item flex-between m-b-20rpx"
          >
            <view class="flex-c">
              <image :src="img" class="qualiCertificate-img m-r-20rpx"></image>
              <view class="u-line-2 text-w text-26rpx c-#333 font-400">
                <mp-html :content="item.content" />
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import img from '@/static/mine/business/mine.png'
import { queryListwg } from '@/interPost/home'

// 数据相关
const pageData = ref([])
const scrollTop = ref(0)
const translateY = ref(0)
const isScrolling = ref(false)
let scrollTimer: any = null
let listHeight = 0

// 参数
const params = ref({
  entity: {
    userId: null,
  },
  orderBy: {},
  page: 1,
  size: 20,
})

// 获取列表
const queryList = async () => {
  const res: any = await queryListwg(params.value)
  if (res.code === 0) {
    pageData.value = res.data.list || []
    // 数据加载完成后开始自动滚动
    nextTick(() => {
      startAutoScroll()
    })
  }
}

// 开始自动滚动
const startAutoScroll = () => {
  if (pageData.value.length === 0) return

  // 计算列表高度
  uni
    .createSelectorQuery()
    .select('.qualiCertificate-list')
    .boundingClientRect((rect: any) => {
      if (rect) {
        listHeight = rect.height / 2 // 因为渲染了两份数据，所以除以2
        autoScroll()
      }
    })
    .exec()
}

// 自动滚动函数
const autoScroll = () => {
  if (isScrolling.value) return

  scrollTimer = setInterval(() => {
    translateY.value -= 1 // 每次向上移动1px

    // 当滚动到一半时重置位置，实现无缝循环
    if (Math.abs(translateY.value) >= listHeight) {
      translateY.value = 0
    }
  }, 100) // 每50ms移动一次，可以调整速度
}

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
    scrollTimer = null
  }
}

// 滚动事件处理
const onScroll = () => {
  isScrolling.value = true
  stopAutoScroll()

  // 停止手动滚动后重新开始自动滚动
  clearTimeout(scrollTimer)
  scrollTimer = setTimeout(() => {
    isScrolling.value = false
    autoScroll()
  }, 2000) // 2秒后重新开始自动滚动
}

onLoad(async () => {
  await uni.$onLaunched
  queryList()
})

onUnmounted(() => {
  stopAutoScroll()
})
</script>

<style lang="scss" scoped>
.violation-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(155deg, #ffdede 0%, #ebeffa 18%, #ffffff 44%, #ffffff 100%);
}

.auto-scroll-container {
  flex: 1;
  overflow: hidden;
}

.scroll-view {
  height: 100%;
  overflow: hidden;
}

.qualiCertificate-list {
  transition: transform 0.05s linear;
}

.qualiCertificate {
  padding: 40rpx;

  .qualiCertificate-btn {
    width: 100%;
    height: 120rpx;
    font-weight: 500;
    line-height: 120rpx;
    color: #fff;
    text-align: center;
    background: rgba(83, 120, 255, 1);
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
  }
}

.qualiCertificate-list {
  padding: 40rpx 40rpx 40rpx;

  .qualiCertificate-item {
    width: 100%;
    // height: 120rpx;
    padding: 4% 40rpx;
    font-weight: 500;
    // line-height: 120rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);

    .qualiCertificate-img {
      width: 50rpx;
      height: 55rpx;
    }

    .text-w {
      width: calc(100% - 75rpx);
    }
  }
}
</style>
