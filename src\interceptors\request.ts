import Request, { HttpRequestConfig, HttpResponse, HttpError } from 'luch-request'
import { BlacklistType } from '@/enum'
const http = new Request()
const baseUrl = import.meta.env.VITE_SERVER_BASEURL
const { baseUrlUriIsEncrypt } = useBaseUrlList()
const { sm4Decrypt, sm4Encrypt, sm4DecryptToString } = useSmCrypto()
interface RequestQueueItem {
  config: HttpRequestConfig
  timestamp: number
}

const LoadingQueue = (() => {
  const queue: RequestQueueItem[] = []
  let timer: number | null = null
  const DELAY = 1000
  const showLoading = () => {
    if (queue.length) {
      uni.showLoading({ title: '加载中...', mask: true })
    }
    return null
  }
  const hideLoading = () => {
    // @ts-expect-error 微信小程序支持 noConflict 参数
    uni.hideLoading({ noConflict: true })
  }
  return {
    add: (config: HttpRequestConfig): void => {
      queue.push({ config, timestamp: Date.now() })
      if (queue.length === 1) {
        timer = setTimeout(showLoading, DELAY)
      }
    },
    remove: (config: HttpRequestConfig): void => {
      const index = queue.findIndex((item) => item.config === config)
      if (index > -1) {
        queue.splice(index, 1)
      }
      if (!queue.length) {
        if (timer) {
          clearTimeout(timer)
          timer = null
        }
        hideLoading()
      }
    },
  }
})()
const emitError = () => {
  uni.$emit('z-paging-error-emit')
}
/** 请求拦截 */
const requestInterceptors = () => {
  http.interceptors.request.use(
    (config) => {
      const custom = config?.custom ?? {}
      const { getToken, userRoleIsBusiness, hrExamineStateObj } = useUserInfo()
      config.header.token = getToken()
      console.log('hrExamineStateObj.value', hrExamineStateObj.value)
      if (
        BlacklistType.BLANK_ROUTER_LIST.includes(config.url) &&
        userRoleIsBusiness.value &&
        hrExamineStateObj.value.status === 1 &&
        hrExamineStateObj.value.auditStatus === 1
      ) {
        console.log('正常触发')
      } else {
        if (
          BlacklistType.BLANK_ROUTER_LIST.includes(config.url) &&
          userRoleIsBusiness.value &&
          hrExamineStateObj.value.status === 0 &&
          hrExamineStateObj.value.auditStatus === 2
        ) {
          uni.showModal({
            title: '提示：',
            content: '您的在职证明审核未通过，请重新上传',
            success: function (res) {
              if (res.confirm) {
                uni.navigateTo({
                  url: '/loginSetting/companyJoin/jobCertificate?source=myInfo',
                })
              } else if (res.cancel) {
                console.log('res.cancel')
              }
            },
          })
          // 这里中断请求
          return Promise.reject(new Error('您的在职证明审核未通过，请重新上传'))
        } else if (
          BlacklistType.BLANK_ROUTER_LIST.includes(config.url) &&
          userRoleIsBusiness.value
        ) {
          uni.showModal({
            title: '提示：',
            content: '您的在职证明正在审核中，请耐心等待',
            success: function (res) {
              if (res.confirm) {
                console.log('res.confirm')
              }
            },
          })
          return Promise.reject(new Error('您的在职证明正在审核中，请耐心等待'))
        }
      }

      console.log('入', config.url, config.data)
      const urlIsEncrypt = baseUrlUriIsEncrypt(config.url)
      config.data = urlIsEncrypt ? { data: sm4Encrypt(config.data) } : config.data || {}
      custom?.loading && LoadingQueue.add(config)
      return config
    },
    (config) => {
      LoadingQueue.remove(config)
      return Promise.reject(config)
    },
  )
}

/**
 * 解密响应数据
 * @param response 响应对象
 * @returns 解密后的响应数据
 */
const decryptResponseData = (response: HttpResponse | HttpError) => {
  const { config } = response

  const custom = config?.custom ?? {}
  const isJson = !!custom?.json
  const urlIsEncrypt = baseUrlUriIsEncrypt(config.url)
  let responseData = response.data
  if (urlIsEncrypt && responseData.data) {
    const encryptedData = responseData.data
    const decryptedData: AnyObject = sm4Decrypt(encryptedData)
    responseData = isJson
      ? { ...decryptedData, data: sm4DecryptToString(encryptedData) }
      : decryptedData
  }
  return {
    responseData,
    config,
    custom,
  }
}

/** 响应拦截 */
const responseInterceptors = () => {
  const { clearUserInfo } = useUserInfo()
  http.interceptors.response.use(
    async (response) => {
      const { responseData, config, custom } = decryptResponseData(response)
      response.data = responseData
      custom?.loading && LoadingQueue.remove(config)
      const data = response.data
      console.log('出', response.config.url, data)
      if (typeof data === 'object' && data.code !== 0) {
        emitError()
        if (!custom?.toast && ![401].includes(data.code)) {
          uni.showToast({
            title: data?.msg ?? '请稍后重试',
            icon: 'none',
          })
        }
        if ([401].includes(data.code)) {
          clearUserInfo()
        }
        return custom?.catch ? Promise.reject(data) : new Promise(() => {})
      }
      return Promise.resolve(data)
    },
    async (response) => {
      try {
        const { responseData, config, custom } = decryptResponseData(response)
        custom?.loading && LoadingQueue.remove(config)
        emitError()
        if (response.statusCode === 401) {
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/login/index',
              success: () => {
                clearUserInfo()
              },
            })
          }, 1000)
        }
        return Promise.reject(response)
      } catch (error) {
        console.log('error', error)
      }
    },
  )
}

const initRequest = () => {
  http.setConfig((defaultConfig) => {
    defaultConfig.baseURL = baseUrl
    defaultConfig.timeout = 30000
    return defaultConfig
  })
  requestInterceptors()
  responseInterceptors()
}

export { initRequest, baseUrl, http, decryptResponseData }
